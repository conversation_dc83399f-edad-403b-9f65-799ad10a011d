#!/usr/bin/env python3
"""
专门处理arg1格式的数据 - 适配Dify代码执行工具
"""
import json


def extract_table_from_arg1(input_str):
    """从arg1格式的数据中提取表格并移除序号列"""
    try:
        # 解析外层JSON
        outer_data = json.loads(input_str)

        # 获取arg1字段
        if "arg1" not in outer_data:
            return "错误: 未找到arg1字段"

        # 解析arg1中的JSON字符串
        inner_data = json.loads(outer_data["arg1"])

        # 提取markdown_table
        if "markdown_table" not in inner_data:
            return "错误: 未找到markdown_table字段"

        # 格式化表格
        table = inner_data["markdown_table"].replace("\\n", "\n")

        # 移除序号列
        lines = table.strip().split("\n")
        if len(lines) >= 2:
            # 处理表头
            header_parts = [
                part.strip() for part in lines[0].split("|")[2:-1]
            ]  # 跳过空字符串、序号列和末尾空字符串
            new_header = "| " + " | ".join(header_parts) + " |"

            # 处理分隔符行
            separator_parts = [
                part.strip() for part in lines[1].split("|")[2:-1]
            ]  # 跳过空字符串、序号列和末尾空字符串
            new_separator = "| " + " | ".join(separator_parts) + " |"

            # 处理数据行
            new_lines = [new_header, new_separator]
            for line in lines[2:]:
                if line.strip():
                    data_parts = [
                        part.strip() for part in line.split("|")[2:-1]
                    ]  # 跳过空字符串、序号列和末尾空字符串
                    new_line = "| " + " | ".join(data_parts) + " |"
                    new_lines.append(new_line)

            return "\n".join(new_lines)
        else:
            return table

    except json.JSONDecodeError as e:
        return f"JSON解析错误: {e}"
    except Exception as e:
        return f"处理错误: {e}"


# Dify代码执行工具环境
# 注意：请确保包含敏感词检测结果的数据通过 arg1 变量传入
# 如果变量名不是 arg1，请修改下面的变量名

try:
    # 执行提取并输出到result变量
    result = extract_table_from_arg1(arg1)
except NameError:
    result = "错误: 未找到 arg1 变量。请确保敏感词检测结果通过 arg1 变量传入，或修改脚本中的变量名。"
except Exception as e:
    result = f"执行错误: {e}"
