import json


def main(detection_data) -> dict:
    """
    从敏感词检测结果中提取markdown表格并移除序号列

    Args:
        detection_data: 包含敏感词检测结果的JSON字符串

    Returns:
        dict: 包含格式化表格的字典
    """
    try:
        # 直接解析detection_data，它本身就是包含敏感词检测结果的JSON字符串
        inner_data = json.loads(detection_data)

        # 提取markdown_table
        if "markdown_table" not in inner_data:
            return {"result": "错误: 未找到markdown_table字段"}

        # 格式化表格
        table = inner_data["markdown_table"].replace("\\n", "\n")

        # 移除序号列
        lines = table.strip().split("\n")
        if len(lines) >= 2:
            # 处理表头
            header_parts = [
                part.strip() for part in lines[0].split("|")[2:-1]
            ]  # 跳过空字符串、序号列和末尾空字符串
            new_header = "| " + " | ".join(header_parts) + " |"

            # 处理分隔符行
            separator_parts = [
                part.strip() for part in lines[1].split("|")[2:-1]
            ]  # 跳过空字符串、序号列和末尾空字符串
            new_separator = "| " + " | ".join(separator_parts) + " |"

            # 处理数据行
            new_lines = [new_header, new_separator]
            for line in lines[2:]:
                if line.strip():
                    data_parts = [
                        part.strip() for part in line.split("|")[2:-1]
                    ]  # 跳过空字符串、序号列和末尾空字符串
                    new_line = "| " + " | ".join(data_parts) + " |"
                    new_lines.append(new_line)

            formatted_table = "\n".join(new_lines)
        else:
            formatted_table = table

        return {"result": formatted_table}

    except json.JSONDecodeError as e:
        return {"result": f"JSON解析错误: {e}"}
    except Exception as e:
        return {"result": f"处理错误: {e}"}


# 测试代码
if __name__ == "__main__":
    test_data = '{"success": true, "message": "检测完成", "total_words": 4, "markdown_table": "| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 |\\n|------|------------|------------|----------|\\n| 1 | 主观词语 | 一般 | 5 |\\n| 2 | 主观词语 | 科学 | 1 |\\n| 3 | 主观词语 | 合理 | 1 |\\n| 4 | 主观词语 | 优秀 | 2 |\\n", "results": [{"序号": 1, "敏感词类型": "主观词语", "敏感词内容": "一般", "出现次数": 5}, {"序号": 2, "敏感词类型": "主观词语", "敏感词内容": "科学", "出现次数": 1}, {"序号": 3, "敏感词类型": "主观词语", "敏感词内容": "合理", "出现次数": 1}, {"序号": 4, "敏感词类型": "主观词语", "敏感词内容": "优秀", "出现次数": 2}]}'

    result = main(test_data)
    print(json.dumps(result, ensure_ascii=False, indent=2))
