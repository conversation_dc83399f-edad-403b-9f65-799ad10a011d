#!/usr/bin/env python3
import json
import sys

def extract_from_file(filename):
    """从文件读取并提取表格"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'arg1' in data:
            inner_data = json.loads(data['arg1'])
            if 'markdown_table' in inner_data:
                table = inner_data['markdown_table'].replace('\\n', '\n')
                return table
            else:
                return "错误: 未找到markdown_table字段"
        else:
            return "错误: 未找到arg1字段"
    
    except Exception as e:
        return f"错误: {e}"

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python extract_file.py filename.json")
        sys.exit(1)
    
    result = extract_from_file(sys.argv[1])
    print(result)
