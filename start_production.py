"""
生产环境启动脚本 - 禁用文件监控和调试模式
"""
import uvicorn
from app.main import app
from app.config import get_settings

if __name__ == "__main__":
    settings = get_settings()
    
    # 生产环境配置
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=False,  # 禁用文件监控
        log_level="info",
        access_log=True,
        workers=1  # 单进程模式，避免多进程问题
    )
