#!/bin/bash

# 创建离线Docker镜像脚本
# 在有网络的机器上运行此脚本

echo "📦 创建离线Docker镜像..."

# 拉取基础镜像
echo "1. 拉取Python基础镜像..."
docker pull python:3.9-slim

# 保存镜像为tar文件
echo "2. 保存基础镜像..."
docker save python:3.9-slim > python-3.9-slim.tar

# 构建应用镜像
echo "3. 构建应用镜像..."
docker build -t sensitive-word-api:latest .

# 保存应用镜像
echo "4. 保存应用镜像..."
docker save sensitive-word-api:latest > sensitive-word-api.tar

echo "✅ 离线镜像创建完成！"
echo "📁 生成的文件:"
echo "  - python-3.9-slim.tar (基础镜像)"
echo "  - sensitive-word-api.tar (应用镜像)"
echo ""
echo "📋 在目标服务器上执行:"
echo "  docker load < python-3.9-slim.tar"
echo "  docker load < sensitive-word-api.tar"
echo "  docker-compose up -d"
