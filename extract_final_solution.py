#!/usr/bin/env python3
"""
最终解决方案：提取markdown表格
解决了编码和参数传递问题
"""
import json
import sys
import os

def extract_table(data):
    """提取表格"""
    try:
        if isinstance(data, str):
            data = json.loads(data)
        
        if 'arg1' in data:
            inner_data = json.loads(data['arg1'])
            if 'markdown_table' in inner_data:
                return inner_data['markdown_table'].replace('\\n', '\n')
        
        return "错误: 未找到数据"
    except Exception as e:
        return f"错误: {e}"

def main():
    """主函数"""
    if len(sys.argv) == 2:
        arg = sys.argv[1]
        
        # 检查是否是文件
        if os.path.isfile(arg):
            try:
                with open(arg, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                result = extract_table(data)
            except Exception as e:
                result = f"文件读取错误: {e}"
        else:
            # 作为JSON字符串处理
            result = extract_table(arg)
    else:
        # 交互式输入
        print("请输入JSON数据（输入完成后按Ctrl+D或Ctrl+Z）:")
        try:
            input_data = sys.stdin.read()
            result = extract_table(input_data)
        except KeyboardInterrupt:
            print("\n操作取消")
            return
        except Exception as e:
            result = f"输入错误: {e}"
    
    print(result)

if __name__ == "__main__":
    main()
