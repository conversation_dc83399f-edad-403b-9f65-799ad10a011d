@echo off
echo 启动稳定版敏感词检测API服务...
echo.

REM 设置生产环境变量
set PRODUCTION=true

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.9+
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装/更新依赖
echo 安装依赖包...
pip install -r requirements.txt

REM 检查敏感词文件
if not exist "sensitive_words.xlsx" (
    echo 警告: 未找到sensitive_words.xlsx文件
    echo 请确保该文件存在于项目根目录
    pause
)

REM 启动服务（生产模式）
echo.
echo 启动API服务（生产模式）...
echo 服务地址: http://localhost:8087
echo API文档: http://localhost:8087/docs
echo.
python main.py --production

pause
