"""
应用程序配置模块
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用程序设置"""

    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8087
    debug: bool = False

    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"

    # 敏感词检测配置
    sensitive_words_file: str = "敏感词列表.xlsx"
    cache_expire_seconds: int = 3600
    max_content_length: int = 1048576  # 1MB

    # API限流配置
    rate_limit_requests: int = 100
    rate_limit_window: int = 60

    # Dify集成配置
    dify_tool_name: str = "sensitive_word_detector"
    dify_tool_version: str = "1.0.0"

    model_config = {"env_file": ".env", "case_sensitive": False, "extra": "allow"}


# 全局设置实例
settings = Settings()


def get_settings() -> Settings:
    """获取应用程序设置"""
    return settings
