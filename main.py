"""
FastAPI敏感词检测应用程序启动文件
"""

import os
import sys
import uvicorn
from app.main import app
from app.config import get_settings

if __name__ == "__main__":
    settings = get_settings()

    # 检查是否为生产环境
    is_production = os.getenv("PRODUCTION", "false").lower() == "true"

    if is_production or "--production" in sys.argv:
        # 生产环境配置
        print("🚀 启动生产环境模式...")
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=False,  # 禁用文件监控
            log_level="info",
            access_log=True,
            workers=1,
        )
    else:
        # 开发环境配置
        print("🔧 启动开发环境模式...")
        print("💡 提示: 使用 --production 参数或设置 PRODUCTION=true 启动生产模式")
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            reload_excludes=[
                "*.log",
                "logs/*",
                ".conda/*",
                "__pycache__/*",
            ],  # 排除不必要的文件监控
        )
