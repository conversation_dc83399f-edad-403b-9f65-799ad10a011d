#!/usr/bin/env python3
import json
import sys

# 读取输入
try:
    if len(sys.argv) > 1:
        # 从参数读取
        input_data = sys.argv[1]
    else:
        # 从stdin读取
        input_data = sys.stdin.read()

    # 解析JSON
    data = json.loads(input_data)

    # 提取表格
    if "arg1" in data:
        inner_data = json.loads(data["arg1"])
        table = inner_data["markdown_table"].replace("\\n", "\n")
        print(table)
    else:
        print("错误: 未找到arg1字段")

except Exception as e:
    print(f"错误: {e}")
    print("请提供正确的JSON格式数据")
