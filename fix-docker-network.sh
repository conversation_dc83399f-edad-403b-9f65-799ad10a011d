#!/bin/bash

echo "🔧 修复Docker网络连接问题..."

# 方案1: 配置Docker代理
echo "1. 配置Docker代理..."
sudo mkdir -p /etc/systemd/system/docker.service.d

# 检查是否有代理环境变量
if [ ! -z "$http_proxy" ] || [ ! -z "$HTTP_PROXY" ]; then
    echo "检测到代理配置，创建Docker代理配置..."
    sudo tee /etc/systemd/system/docker.service.d/http-proxy.conf > /dev/null <<EOF
[Service]
Environment="HTTP_PROXY=${http_proxy:-$HTTP_PROXY}"
Environment="HTTPS_PROXY=${https_proxy:-$HTTPS_PROXY}"
Environment="NO_PROXY=${no_proxy:-$NO_PROXY}"
EOF
    
    # 重启Docker服务
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    echo "✅ Docker代理配置完成"
else
    echo "未检测到代理配置"
fi

# 方案2: 配置Docker镜像源
echo "2. 配置Docker镜像源..."
sudo mkdir -p /etc/docker

# 创建daemon.json配置文件
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF

# 重启Docker服务
sudo systemctl daemon-reload
sudo systemctl restart docker

echo "✅ Docker镜像源配置完成"

# 方案3: 测试网络连接
echo "3. 测试网络连接..."
if curl -I --connect-timeout 10 https://registry-1.docker.io > /dev/null 2>&1; then
    echo "✅ Docker Hub连接正常"
else
    echo "❌ Docker Hub连接失败"
    echo "建议检查网络连接或防火墙设置"
fi

echo "🎉 修复完成！请重新运行 ./deploy.sh"
