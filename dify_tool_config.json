{"identity": {"author": "敏感词检测工具", "name": "sensitive_word_detector", "label": {"en_US": "Sensitive Word Detector", "zh_Hans": "敏感词检测器"}, "description": {"en_US": "Detect sensitive words in markdown content and return results in markdown table format", "zh_Hans": "检测markdown内容中的敏感词，并以markdown表格格式返回结果"}, "icon": "🔍"}, "parameters": [{"name": "content", "type": "string", "required": true, "label": {"en_US": "Content", "zh_Hans": "内容"}, "human_description": {"en_US": "The markdown content to be detected for sensitive words", "zh_Hans": "要检测敏感词的markdown内容"}, "llm_description": "The markdown content that needs to be analyzed for sensitive words. Supports page markers like <!-- page:1 --> for page tracking.", "form": "llm", "default": ""}], "credentials_for_provider": {}, "runtime": {"type": "api", "api": {"endpoint": "http://localhost:8087/detect", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"content": "{{content}}"}}}}