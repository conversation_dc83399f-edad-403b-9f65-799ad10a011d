"""
缓存管理模块
"""
import time
import hashlib
from typing import Dict, Any, Optional
from loguru import logger


class SimpleCache:
    """简单的内存缓存实现"""
    
    def __init__(self, default_expire_seconds: int = 3600):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_expire_seconds = default_expire_seconds
    
    def _generate_key(self, content: str) -> str:
        """生成缓存键"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def get(self, content: str) -> Optional[Any]:
        """获取缓存值"""
        key = self._generate_key(content)
        
        if key not in self.cache:
            return None
        
        cache_item = self.cache[key]
        
        # 检查是否过期
        if time.time() > cache_item['expire_time']:
            del self.cache[key]
            logger.debug(f"缓存项已过期并删除: {key}")
            return None
        
        logger.debug(f"缓存命中: {key}")
        return cache_item['value']
    
    def set(self, content: str, value: Any, expire_seconds: Optional[int] = None) -> None:
        """设置缓存值"""
        key = self._generate_key(content)
        expire_time = time.time() + (expire_seconds or self.default_expire_seconds)
        
        self.cache[key] = {
            'value': value,
            'expire_time': expire_time,
            'created_time': time.time()
        }
        
        logger.debug(f"缓存已设置: {key}")
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
        logger.info("缓存已清空")
    
    def cleanup_expired(self) -> int:
        """清理过期的缓存项"""
        current_time = time.time()
        expired_keys = []
        
        for key, cache_item in self.cache.items():
            if current_time > cache_item['expire_time']:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        total_items = len(self.cache)
        expired_items = 0
        
        for cache_item in self.cache.values():
            if current_time > cache_item['expire_time']:
                expired_items += 1
        
        return {
            'total_items': total_items,
            'active_items': total_items - expired_items,
            'expired_items': expired_items,
            'memory_usage_estimate': len(str(self.cache))  # 简单的内存使用估算
        }


# 全局缓存实例
detection_cache = SimpleCache()
